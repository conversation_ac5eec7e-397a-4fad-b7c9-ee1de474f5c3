import os
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain.vectorstores import Chroma
from langchain.embeddings import OpenAIEmbeddings

def merge_markdown_files(base_dir: str) -> str:
    merged = ""
    for root, _, files in os.walk(base_dir):
        for file in files:
            if file.endswith(".md"):
                path = os.path.join(root, file)
                with open(path, "r", encoding="utf-8") as f:
                    content = f.read()
                    merged += f"\n\n# {file}\n{content}"
    return merged

def split_by_title(text: str):
    chunks = text.split("\n# ")
    cleaned = ["# " + c.strip() if not c.startswith("#") else c.strip() for c in chunks if c.strip()]
    return cleaned


def build_qa_chain():
    prompt = PromptTemplate(
        input_variables=["answer"],
        template="""
아래는 고객에게 제공된 안내문입니다. 고객이 이 내용을 받기 전에 어떤 질문을 했을지 유추하여 "Q:"로 시작하는 질문과 "A:"로 시작하는 답변을 구성하세요.

답변:
{answer}

형식:
Q: (예상 질문)
A: (원본 답변)
"""
    )
    llm = ChatOpenAI(model_name="gpt-4", temperature=0.3)
    return LLMChain(prompt=prompt, llm=llm)



def build_qa_chain_json():
    prompt = PromptTemplate(
        input_variables=["answer"],
        template="""
아래는 고객에게 제공된 안내문입니다. 고객이 이 내용을 받기 전에 어떤 질문을 했을지 유추하여 JSON 형식으로 반환하세요.

반환 형식:
{{
  "faq_id": "FAQ###",       // 고유 ID는 FAQ001부터 자동 증가하거나 생략 가능
  "category": "",           // 적절한 카테고리를 추정해서 넣어주세요 (예: 배송, 교환, 설치 등)
  "question": "",           // 유추된 질문
  "answer": "",             // 원본 답변
  "keywords": ""            // 질문/답변에 포함된 핵심 키워드 (쉼표로 구분)
}}

답변:
{answer}
"""
    )
    llm = ChatOpenAI(model_name="gpt-4", temperature=0.3)
    return LLMChain(prompt=prompt, llm=llm)


def generate_documents(chunks, chain):
    documents = []
    for chunk in chunks:
        qa = chain.run(answer=chunk)
        documents.append(Document(page_content=qa, metadata={}))
    return documents

def store_to_chroma(documents, persist_dir="./chroma_notion_qa"):
    embeddings = OpenAIEmbeddings()
    db = Chroma.from_documents(documents, embeddings, persist_directory=persist_dir)
    db.persist()
    return db

from pathlib import Path
import os

base_dir = Path.cwd().parents[1] / "SKN13-3rd-4TEAM" / "data" / "raw_docs" / "qna_raw"
print(f"🔍 탐색 경로: {base_dir}")

merged_text = ""

for root, _, files in os.walk(base_dir):
    for file in files:
        if file.lower().endswith(".md"):
            path = os.path.join(root, file)
            print(f"📄 병합 중: {path}")
            with open(path, "r", encoding="utf-8") as f:
                content = f.read()
                merged_text += f"\n\n{content}"

print("✅ 병합 완료" if merged_text.strip() else "⚠ 병합된 내용 없음")


root_dir = Path.cwd().parents[0]
root_dir

# 병합된 내용을 저장할 경로 설정
output_path = root_dir / "data" / "merged_docs" / "merged.md"

# 폴더 없으면 생성
output_path.parent.mkdir(parents=True, exist_ok=True)

# 파일로 저장
with open(output_path, "w", encoding="utf-8") as f:
    f.write(merged_text)

print(f"✅ 병합 완료: 저장 위치 → {output_path}")

len(merged_text)


print("✂️ 텍스트 분할 중...")
chunks = split_by_title(merged_text)


chunks[:20]


print("💡 질문 생성 중...")
chain = build_qa_chain()
docs = generate_documents(chunks, chain)


for i, doc in enumerate(docs[:15]):  # 앞부분만 샘플 확인
    print(f"\n🔹 Document {i+1}:\n{doc.page_content}\n{'-'*60}")


print("📦 Chroma 저장 중...")
store_to_chroma(docs, persist_dir="../data/vectordb_chroma")

print("✅ 완료: 총 문서 수 =", len(docs))



#OCR테스트 
%pip install google-cloud-vision


import os
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "credential.json"

from google.cloud import vision
from google.cloud.vision_v1 import types

def google_vision_ocr(image_path):
    client = vision.ImageAnnotatorClient()
    
    with open(image_path, 'rb') as img_file:
        content = img_file.read()

    image = vision.Image(content=content)
    response = client.text_detection(image=image)
    texts = response.text_annotations

    if not texts:
        return "❌ 텍스트 인식 실패"

    return texts[0].description.strip()



result = google_vision_ocr("test.jpg")
print("🧾 OCR 결과:\n", result)
