"""
SQLite 데이터베이스에 JSON 데이터 로드 스크립트
"""
import sqlite3
import json
import os
from pathlib import Path

# 프로젝트 루트 경로
project_root = Path(__file__).parent.parent
data_dir = project_root / "data"
db_path = data_dir / "sample_db" / "ecommerce.db"
raw_docs_path = data_dir / "raw_docs"

def load_json_data(file_path):
    """JSON 파일 로드"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def insert_users(conn):
    """사용자 데이터 삽입"""
    users_path = raw_docs_path / "sample_users.json"
    if not users_path.exists():
        print(f"⚠️ 파일을 찾을 수 없음: {users_path}")
        return
        
    users_data = load_json_data(users_path)
    
    cursor = conn.cursor()
    for user in users_data:
        cursor.execute("""
            INSERT OR REPLACE INTO users 
            (user_id, username, email, phone, address, member_grade, join_date, total_orders, total_amount)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            user['user_id'], user['username'], user['email'], user['phone'],
            user['address'], user['member_grade'], user['join_date'],
            user['total_orders'], user['total_amount']
        ))
    
    conn.commit()
    print(f"✅ 사용자 데이터 {len(users_data)}건 삽입 완료")

def insert_products(conn):
    """상품 데이터 삽입"""
    products_path = raw_docs_path / "product_info.json"
    if not products_path.exists():
        print(f"⚠️ 파일을 찾을 수 없음: {products_path}")
        return
        
    products_data = load_json_data(products_path)

    cursor = conn.cursor()
    for product in products_data:
        # 필드명 호환성 처리
        product_id = product.get('product_id') or product.get('id')
        keywords = product.get('keywords', [])

        cursor.execute("""
            INSERT OR REPLACE INTO products
            (product_id, name, category, description, specifications, features, price, keywords)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            product_id, product['name'], product['category'],
            product['description'], json.dumps(product['specifications'], ensure_ascii=False),
            json.dumps(product['features'], ensure_ascii=False), product['price'],
            json.dumps(keywords, ensure_ascii=False)
        ))

    conn.commit()
    print(f"✅ 상품 데이터 {len(products_data)}건 삽입 완료")

def insert_orders(conn):
    """주문 데이터 삽입"""
    orders_path = raw_docs_path / "sample_orders.json"
    if not orders_path.exists():
        print(f"⚠️ 파일을 찾을 수 없음: {orders_path}")
        return
        
    orders_data = load_json_data(orders_path)
    
    cursor = conn.cursor()
    for order in orders_data:
        # 주문 정보 삽입
        cursor.execute("""
            INSERT OR REPLACE INTO orders 
            (order_id, user_id, order_date, status, tracking_number, delivery_company, total_amount, shipping_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            order['order_id'], order['user_id'], order['order_date'],
            order['status'], order['tracking_number'], order['delivery_company'],
            order['total_amount'], order['shipping_address']
        ))
        
        # 주문 상품 정보 삽입
        for item in order['items']:
            cursor.execute("""
                INSERT OR REPLACE INTO order_items 
                (order_id, product_id, product_name, quantity, price)
                VALUES (?, ?, ?, ?, ?)
            """, (
                order['order_id'], item['product_id'], item['product_name'],
                item['quantity'], item['price']
            ))
    
    conn.commit()
    print(f"✅ 주문 데이터 {len(orders_data)}건 삽입 완료")

def insert_faq(conn):
    """FAQ 데이터 삽입"""
    faq_path = raw_docs_path / "faq_data.json"
    if not faq_path.exists():
        print(f"⚠️ 파일을 찾을 수 없음: {faq_path}")
        return
        
    faq_data = load_json_data(faq_path)

    cursor = conn.cursor()
    for faq in faq_data:
        keywords = faq.get('keywords', '')

        # keywords가 문자열이면 배열로 변환
        if isinstance(keywords, str):
            keywords = keywords.split() if keywords else []

        cursor.execute("""
            INSERT OR REPLACE INTO faq
            (category, question, answer, keywords)
            VALUES (?, ?, ?, ?)
        """, (
            faq['category'], faq['question'],
            faq['answer'], json.dumps(keywords, ensure_ascii=False)
        ))

    conn.commit()
    print(f"✅ FAQ 데이터 {len(faq_data)}건 삽입 완료")

def insert_delivery_info(conn):
    """배송 정보 데이터 삽입"""
    delivery_path = raw_docs_path / "delivery_status.json"
    if not delivery_path.exists():
        print(f"⚠️ 파일을 찾을 수 없음: {delivery_path}")
        return
        
    delivery_data = load_json_data(delivery_path)
    
    cursor = conn.cursor()
    for delivery in delivery_data:
        cursor.execute("""
            INSERT OR REPLACE INTO delivery_info 
            (tracking_number, delivery_company, status, current_location, delivery_date, estimated_delivery, recipient, tracking_history)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            delivery['tracking_number'], delivery['delivery_company'], delivery['status'],
            delivery['current_location'], delivery.get('delivery_date'), 
            delivery.get('estimated_delivery'), delivery['recipient'],
            json.dumps(delivery['tracking_history'], ensure_ascii=False)
        ))
    
    conn.commit()
    print(f"✅ 배송 정보 데이터 {len(delivery_data)}건 삽입 완료")

def main():
    """메인 실행 함수"""
    print("🚀 데이터베이스 데이터 로드 시작...")
    
    if not db_path.exists():
        print(f"❌ 데이터베이스 파일이 없습니다: {db_path}")
        print("💡 먼저 다음 명령어를 실행하세요:")
        print("   python db/init_db.py")
        return 1
    
    try:
        # 데이터베이스 연결
        conn = sqlite3.connect(db_path)
        
        # 샘플 데이터 삽입
        insert_users(conn)
        insert_products(conn)
        insert_orders(conn)
        insert_faq(conn)
        insert_delivery_info(conn)
        
        conn.close()
        print("✅ 데이터베이스 데이터 로드 완료!")
        return 0
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())