{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 고객의 id 를 받아 고객 개인정보 테이블에 연결하는 DB\n", "# 챗봇 : FAQ, 제품 설명서 등을 RAG 기반으로 검색 및 답변 | 개인 관련 내용은 history로 입력\n", "# 배송 API를 어떻게 구성하지?"]}, {"cell_type": "code", "execution_count": null, "id": "86f4d705", "metadata": {}, "outputs": [], "source": ["# 역할 분담\n", "\n", "# 파일 로드 - 제품설명서 + FAQ 목록 -> vectorDB 에 넣을 거\n", "# code/documents.ipynb\n", "## 가져와서 metadata 랑 chunking 전처리하고 vectorDB에 넣기까지\n", "\n", "# RAG 구조 + 모델링 - 메타 데이터 및 쿼리 재생성(HyDE) + vectorDB에서 검색\n", "# code/main.ipynb\n", "## retriever 생성, 문서 추출 함수 등 \n", "## SQL Agent 까지 여기서 구성\n", "\n", "# 개인정보용 SQLDataBase 구성 - 고객 정보 테이블, 고객 장바구니?, 고객 주문내역\n", "## 해당 정보 관련 query 일 경우 별도의 tool로 개인정보 가져오기\n", "\n", "# RAG 평가 지표 구성\n", "# 새 파일 \n", "## RAGAS 이용, 지표 골라야함"]}, {"cell_type": "code", "execution_count": null, "id": "18718132", "metadata": {}, "outputs": [], "source": ["# vector_store 연결\n", "# from documents import get_vector_store    # ipynb 파일이라 안되네.. 나중에 다쓰고 py 파일 만들면 바꾸자 \n", "from langchain_openai import OpenAIEmbeddings\n", "from dotenv import load_dotenv\n", "from langchain_chroma import Chroma\n", "\n", "COLLECTION_NAME = \"product_usage\"\n", "PERSISTENT_PATH = \"../data/vector_store/chroma/FAQ_db\"\n", "def get_vector_store():\n", "    load_dotenv()\n", "    embedding_model = OpenAIEmbeddings(model='text-embedding-3-large')\n", "\n", "    vector_store = Chroma(\n", "        embedding_function=embedding_model,\n", "        collection_name=COLLECTION_NAME,\n", "        persist_directory=PERSISTENT_PATH\n", "    )\n", "    return vector_store\n", "\n", "# Retriever 생성\n", "def get_retriever(k=10):\n", "    vector_store = get_vector_store()\n", "    retriever = vector_store.as_retriever(search_kwargs={\"k\":k})\n", "    return retriever\n", "retriever = get_retriever(k=3)\n", "\n", "# 문서 내용 추출 함수 \n", "from langchain_core.documents import Document\n", "def format_docs(docs:list[Document]) -> str:\n", "    \"\"\"\n", "    Retriever가 검색한 문서들에서 page_content(문서 내용) 만 추출해서 반환.\n", "    추출된 문서들의 내용을 \"\\n\\n\"로 연결한다.\n", "    Args:\n", "        docs(list[Document]) - 검색한 문서 리스트\n", "    Returns:\n", "        str - 문서1내용+\\n\\n문서2내용+\\n\\n .. \n", "    \"\"\"\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "# HyDE 를 적용해 retriever 호출 함수\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from textwrap import dedent\n", "\n", "def hyde_retriever(query:str, k=3):\n", "    hyde_model = ChatOpenAI(model_name='gpt-4.1')   # 가상답변을 생성하는 모델은 좋은 모델을 사용해야함. 할루시네이션 방지\n", "    hyde_prompt_template = PromptTemplate(\n", "    template=dedent(\"\"\"# Instruction\n", "        다음 질문에 대해서 완전하고 상세한 답변을 실제 사실에 기반해서 작성해 주세요.\n", "        질문과 관련된 내용만으로 답변을 작성합니다.\n", "        답변과 직접적인 연관성이 없는 내용은 답변에 포함시키지 않습니다.\n", "\n", "        # 질문 : \n", "        {query}\n", "        \"\"\")\n", "    )\n", "    hyde_chain = hyde_prompt_template | hyde_model | StrOutputParser()\n", "    retriever = get_retriever(k)\n", "    dummy_answer = hyde_chain.invoke({\"query\":query})\n", "    return retriever.invoke(dummy_answer)"]}, {"cell_type": "code", "execution_count": 5, "id": "cbe65973", "metadata": {}, "outputs": [], "source": ["# 메인 챗봇 모델 구성\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnableWithMessageHistory, RunnablePassthrough, RunnableLambda\n", "from langchain_community.chat_message_histories import SQLChatMessageHistory\n", "from sqlalchemy import create_engine\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "# SQL 연결\n", "session_id = 'user_1'\n", "engine = create_engine(\"mysql+pymysql://jin:1111@localhost:3306/hr\")\n", "sql_store = {}\n", "def get_session_history(session_id:str)->SQLChatMessageHistory:\n", "    if session_id not in sql_store:\n", "        sql_store[session_id] = SQLChatMessageHistory(\n", "            session_id=session_id,\n", "            connection=engine\n", "        )\n", "    return sql_store[session_id]\n", "\n", "# 체인 구성\n", "system_template = \"\"\"### Instruction:\n", "    당신은 AI 전문가입니다.\n", "    \n", "    ### Context\n", "    {context}\n", "    \"\"\"\n", "    \n", "prompt_template = ChatPromptTemplate(\n", "    [\n", "        (\"system\", system_template),\n", "        MessagesPlaceholder(variable_name=\"history\", optional=True),\n", "        (\"human\", '{query}')\n", "    ]\n", ")\n", "model = ChatOpenAI(model=\"gpt-4.1\")\n", "parser = StrOutputParser()\n", "chain = ({\"context\":RunnableLambda(lambda x: x[\"query\"])|hyde_retriever|format_docs, \n", "        \"query\":RunnablePassthrough()} \n", "        | prompt_template\n", "        | model\n", "        | StrOutputParser()\n", "        )\n", "chain_with_history = RunnableWithMessageHistory(\n", "    runnable=chain,\n", "    get_session_history=get_session_history,\n", "    input_messages_key=\"query\",\n", "    history_messages_key=\"history\"\n", ")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c4422ead", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["query :  제품 배송 얼마나 걸려?\n", "response :  제품 배송 기간은 상품 종류와 브랜드에 따라 다르게 적용됩니다. 아래에서 각 유형별로 안내드립니다.\n", "\n", "---\n", "\n", "### 1. 프린트베이커리 상품\n", "- **배송 기간:** 일반적으로 평일 기준 3~4일 이내 배송 출발(지역에 따라 최대 7일 이상 소요될 수 있습니다).\n", "- **주문 및 배송 절차**\n", "  1. 주문 완료 후 3일 이내에 프린트베이커리에서 배송비 및 배송 방식 관련해 개별 연락\n", "  2. 안내 받은 결제창에서 배송비 결제 진행\n", "  3. 배송비 결제 확인 후 개별 연락을 통해 배송/설치 일정 확인\n", "  4. 일정에 맞춰 배송 진행\n", "- **참고:** 예술 작품 특성상 배송비 추가 및 추가 비용이 발생할 수 있으며, 지역 또는 주문제작 여부에 따라 배송 기간이 더 길어질 수 있습니다.\n", "\n", "---\n", "\n", "### 2. 주문 제작 상품\n", "- **제작/출고 기간:** 결제일 기준 평균 10~15일(평일 기준, 브랜드마다 출고일정 상이)\n", "- **배송 기간:** 출고 후 실제 배송까지 추가로 시간이 소요될 수 있습니다.\n", "- **주의:** 제작이 시작된 이후에는 옵션 변경 및 취소가 어렵고, 명절·공휴일은 기간에서 제외됩니다.\n", "\n", "---\n", "\n", "### 3. 일반 배송 상품 (프린트베이커리 외 브랜드)\n", "- **출고 기간:** 평일 기준 출고되며, 브랜드마다 상이(상세페이지의 [출고 정보] 참고)\n", "- **배송:** 무신사스토어 전 상품 무료배송\n", "- **지연 안내:** 출고 지연 시 상품명에 [지연] 아이콘 및 문자/알림톡으로 안내\n", "- **참고:** 배송 희망 일자 지정은 불가\n", "\n", "---\n", "\n", "**정확한 배송 기간 및 출고 일정은 상품 상세페이지의 배송/출고 정보에서 꼭 확인해 주세요.**\n", "[배송조회 FAQ 바로가기](https://www.musinsa.com/app/cs/faq?idx=32)\n", "\n", "궁금하신 상품의 정확한 이름이나 브랜드를 알려주시면 더 구체적으로 안내드릴 수 있습니다.\n"]}], "source": ["# 실행\n", "query = input()\n", "response = chain_with_history.invoke(\n", "    {\"query\":query}, \n", "    {\"configurable\":{\"session_id\":session_id}})\n", "print(\"query : \", query)\n", "print(\"response : \", response)"]}, {"cell_type": "code", "execution_count": null, "id": "826f3373", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 28, "id": "fc2e3e04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting webdriver_manager\n", "  Using cached webdriver_manager-4.0.2-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from webdriver_manager) (2.32.3)\n", "Requirement already satisfied: python-dotenv in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from webdriver_manager) (1.1.0)\n", "Requirement already satisfied: packaging in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from webdriver_manager) (24.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from requests->webdriver_manager) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from requests->webdriver_manager) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from requests->webdriver_manager) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\anaconda3\\envs\\lang_env\\lib\\site-packages (from requests->webdriver_manager) (2025.4.26)\n", "Using cached webdriver_manager-4.0.2-py2.py3-none-any.whl (27 kB)\n", "Installing collected packages: webdriver_manager\n", "Successfully installed webdriver_manager-4.0.2\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install webdriver_manager"]}, {"cell_type": "code", "execution_count": 20, "id": "3c6954ee", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from bs4 import BeautifulSoup\n", "import time\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from textwrap import dedent\n", "from uuid import uuid4"]}, {"cell_type": "code", "execution_count": null, "id": "3c0a0031", "metadata": {}, "outputs": [], "source": ["# SSF 몰\n", "\n", "# 1. Selenium 드라이버 실행\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))\n", "url = \"https://www.lfmall.co.kr/app/product/HSTS5B206B2?has_init=Y\"\n", "# url = \"https://www.lfmall.co.kr/app/exhibition/menu/502\"\n", "url = \"https://www.lfmall.co.kr/app/product/HZTS5B894BK\"\n", "url = \"https://www.ssfshop.com/ranking?rankSect=CLICK_RANK&ctgryFlterCd=CTGRY_FEMALE&preferAgeCd=ALL&brndShopId=&brandShopNo=&dspCtgryNo=&otltYn=&cnncCtgryNo=&lagSpcltyYn=&utag=\"\n", "\n", "driver.get(url)\n", "time.sleep(3)  # 페이지 로딩 대기\n", "\n", "# 2. 스크롤 내리기\n", "last_height = driver.execute_script(\"return document.body.scrollHeight\")\n", "\n", "while True:\n", "    # 스크롤 맨 아래로 내리기\n", "    driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n", "    \n", "    # 로딩 대기\n", "    time.sleep(2)\n", "\n", "    # 현재 페이지 높이 확인\n", "    new_height = driver.execute_script(\"return document.body.scrollHeight\")\n", "    \n", "    # 더 이상 스크롤이 내려가지 않으면 종료\n", "    if new_height == last_height:\n", "        break\n", "    last_height = new_height\n", "\n", "# 3. 상품 리스트 만들기\n", "product_list = driver.find_elements(By.CSS_SELECTOR, \"#clickNowForm > section > div:nth-child(2) > div.list_goods > ul > li > a\")\n", "print(len(product_list))\n", "\n", "# 4. 각 url 따기\n", "urls = []\n", "for product in product_list:\n", "    url = product.get_attribute(\"href\")\n", "    urls.append(url)\n", "print(len(urls))\n", "\n", "driver.quit()"]}, {"cell_type": "code", "execution_count": null, "id": "e154d8e8", "metadata": {}, "outputs": [], "source": ["# url 저장\n", "import os\n", "os.makedirs(\"../data/urls\", exist_ok=True)\n", "with open(\"../data/urls/ssf_mall_urls.txt\", \"w\") as f:\n", "    f.write(\"\\n\".join(urls))"]}, {"cell_type": "code", "execution_count": 21, "id": "a71c1af7", "metadata": {}, "outputs": [], "source": ["# url 불러오기\n", "def load_urls(file_path):\n", "    with open(file_path, \"r\") as f:\n", "        urls = f.read().splitlines()\n", "    return urls\n", "urls = load_urls(\"../data/urls/ssf_mall_urls.txt\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3b8ab0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'퍼프 소매 오픈워크 카디건 - 네이비' 상품 정보를 추가 저장했습니다.\n", "5.265328645706177 초 걸렸습니다\n", "'코튼 경량 세미와이드 팬츠 - 아이보리' 상품 정보를 추가 저장했습니다.\n", "5.114678621292114 초 걸렸습니다\n", "'스트레이트 리넨 데님 - 네이비' 상품 정보를 추가 저장했습니다.\n", "6.352813720703125 초 걸렸습니다\n", "'체크 스모킹 원피스 - 네이비' 상품 정보를 추가 저장했습니다.\n", "5.5996973514556885 초 걸렸습니다\n", "'레이어드 반소매 카디건 - 그레이' 상품 정보를 추가 저장했습니다.\n", "5.163498401641846 초 걸렸습니다\n", "'플라워 프린트 슬리브리스 롱 원피스 - 네이비' 상품 정보를 추가 저장했습니다.\n", "7.568906307220459 초 걸렸습니다\n", "'텍스처 슬리브리스 롱원피스 - 화이트' 상품 정보를 추가 저장했습니다.\n", "9.066749095916748 초 걸렸습니다\n", "'코튼피치 레이어드 원피스 - 블랙' 상품 정보를 추가 저장했습니다.\n", "5.473812580108643 초 걸렸습니다\n", "'버뮤다 데님 쇼츠 - 블루' 상품 정보를 추가 저장했습니다.\n", "6.24275279045105 초 걸렸습니다\n", "'중기장 부츠컷 경량데님 - 스카이 블루' 상품 정보를 추가 저장했습니다.\n", "5.529244661331177 초 걸렸습니다\n"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "from time import time\n", "\n", "# 5. LLM 모델 준비\n", "model = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "# 6. Prompt Template (중괄호 이스케이프)\n", "prompt_template = ChatPromptTemplate.from_template(dedent(\"\"\"\n", "다음은 상품 상세 페이지의 HTML 일부 소스입니다.\n", "\n", "이 HTML에는 상품에 대한 다양한 정보가 포함되어 있습니다.  \n", "다음 조건에 맞게 JSON 형식으로 변환해주세요:\n", "\n", "1. 상품과 관련된 정보는 가능한 한 모두 담아주세요.  \n", "2. HTML 내 존재하는 정보는 최대한 구체적으로 추출해 주세요.  \n", "3. 아래와 같은 JSON 구조를 따르세요. 단, 절대로, '```json', 'json', ' \\\"\\\"\\\" ', '**설명**' 등과 같이 불필요한 값은 채워넣지 않습니다.\n", "4. 만약 특정 항목이 HTML에 없다면 해당 항목은 생략하지 말고 빈 문자열(\"\")로 채워 주세요.  \n", "5. id 항목은 \"{product_id}\" 로 채워주세요. 쌍따옴표도 포함입니다. id, name, category, description 항목은 필수로 채워야합니다.\n", "6. specifications과 features 항목은 표, 리스트, 상세 스펙 등이 있을 경우나 제품에 대한 서술이 있을 경우 포함해주세요.\n", "7. 배송, 카드사 혜택, 포인트 적립 등의 공통 정보는 제외하고 상품에 직접적으로 관련된 정보만 포함하세요.\n", "8. features 항목은 제품의 특징을 포함합니다. 예를 들어 \"깔끔한 디자인\", \"고급스러운 소재\", \"가벼운 코튼 소재\", \"봄부터 여름까지 착용 가능한 제품\" 등과 같습니다.\n", "9. category 항목은 상품의 카테고리 또는 분류명을 포함합니다. 예를 들어 \"여성 의류 가디건\", \"남성 하의 팬츠\", \"액세서리 귀걸이\" 등입니다.\n", "10. description 항목은 상세 설명을 포함합니다. 길어도 좋으며, 제품의 특징을 최대한 포함하고자 합니다.\n", "\n", "JSON 출력 예시 형태:\n", "\n", "{{\n", "  \"id\": {product_id}\n", "  \"name\": \"상품명\",\n", "  \"category\": \"카테고리 또는 분류명\",\n", "  \"description\": \"상세 설명. 길어도 좋음.\",\n", "  \"price\": \"가격 정보 (할인 전/후 가격 포함 가능)\",\n", "  \"specifications\": [\n", "    \"스펙 1\",\n", "    \"스펙 2\",\n", "    ...\n", "  ],\n", "  \"features\": [\n", "    \"특징 1\",\n", "    \"특징 2\",\n", "    ...\n", "  ],\n", "  \"keywords\": [\n", "    \"키워드 1\",\n", "    \"키워드 2\",\n", "    ...\n", "  ]\n", "}}\n", "\n", "다음은 HTML 소스입니다:\n", "{input_data}\n", "\n", "\"\"\"))\n", "\n", "url_test = urls[:50]\n", "# 7. 각 상품별로 들어가서 html 가져오기\n", "for idx, url in enumerate(urls, start=1):\n", "    e = time()\n", "    try:\n", "        response = requests.get(url)\n", "        soup = BeautifulSoup(response.text, 'html.parser')\n", "        target_1 = soup.select_one(\"#content > section > div.gods-summary > div.godsInfo-area\")\n", "        target_2 = soup.select_one(\"#godsTabView > div.gods-detail-desc.sticky-start > div\")\n", "        \n", "\n", "        # 둘 중 없는 경우 대비\n", "        target_1_text = str(target_1) if target_1 else \"\"\n", "        target_2_text = str(target_2) if target_2 else \"\"\n", "\n", "        # 두 영역 합치기\n", "        extracted_html = target_1_text + \"\\n\\n\" + target_2_text\n", "\n", "        # 체인 실행\n", "        product_id = uuid4()\n", "        chain = prompt_template | model\n", "        result = chain.invoke({\"input_data\": extracted_html, \"product_id\": product_id})\n", "\n", "        # 결과 출력\n", "        # print(result.content)\n", "\n", "        # 파일에 저장\n", "        import json\n", "        import os\n", "\n", "        new_data_text = result.content  # LLM 응답 JSON 문자열\n", "\n", "        try:\n", "            new_data = json.loads(new_data_text)\n", "        except json.JSONDecodeError as error:\n", "            print(\"JSON 파싱 에러:\", error)\n", "            print(\"new_data_text : \", new_data_text)\n", "            new_data = None\n", "            continue\n", "\n", "        if new_data is None:\n", "            print(\"유효한 JSON 데이터가 아니므로 종료합니다.\")\n", "        else:\n", "            filename = 'product_info.json'\n", "\n", "        # 기존 데이터 불러오기 (파일 없으면 빈 리스트로 시작)\n", "        if os.path.exists(filename):\n", "            with open(filename, 'r', encoding='utf-8') as f:\n", "                try:\n", "                    existing_data = json.load(f)\n", "                except json.JSONDecodeError:\n", "                    existing_data = []\n", "        else:\n", "            existing_data = []\n", "\n", "        # existing_data가 리스트인지 확인. 아니면 리스트로 감싸기\n", "        if not isinstance(existing_data, list):\n", "            existing_data = [existing_data]\n", "\n", "        # 같은 name 있는지 검사\n", "        exists = any(item.get('name') == new_data.get('name') for item in existing_data)\n", "\n", "        if exists:\n", "            print(f\"'{new_data.get('name')}' 상품이 이미 존재합니다. 추가하지 않습니다.\")\n", "        else:\n", "            existing_data.append(new_data)\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                json.dump(existing_data, f, ensure_ascii=False, indent=2)\n", "            print(f\"'{new_data.get('name')}' 상품 정보를 추가 저장했습니다.\")\n", "\n", "    except Exception as e:\n", "        print(f\"[{idx}] 에러 발생:\", e)\n", "        continue\n", "    finally:\n", "        s = time()\n", "        print(f\"{s-e} 초 걸렸습니다\")\n"]}, {"cell_type": "code", "execution_count": 30, "id": "daa3ec0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["UUID('0bcc0dc1-b077-4473-9abe-fed315f1ed35')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["uuid4()"]}, {"cell_type": "code", "execution_count": null, "id": "e9146c3d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:34: <PERSON><PERSON>tax<PERSON>arning: invalid escape sequence '\\+'\n", "<>:34: <PERSON><PERSON>tax<PERSON>arning: invalid escape sequence '\\+'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_54204\\3841379026.py:34: SyntaxWarning: invalid escape sequence '\\+'\n", "  target_2 = soup.select_one(\"#root > main > div > div.Product_divideWrap__\\+Gvda > div.Product_rightWrap__vLWpx > div:nth-child(1)\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"id\": \"HZTS5B894BK\",\n", "  \"name\": \"[25SS][시즌오프][LF몰 단독] 블랙 쿨링 벤추리 헨리넥 티셔츠\",\n", "  \"category\": \"티셔츠\",\n", "  \"description\": \"※ 블랙 컬러 티셔츠는 6월 17일 이후 재입고된 제품부터 기존 제품보다 조금 더 짙은 톤으로 변경되었습니다. 구매 시 참고 부탁드립니다.\\n\\n착용감이 가볍고 시원한 소재를 사용하였으며 데일리 룩을 완성시킬 수 있는 아이템입니다.\",\n", "  \"price\": \"정가 109,000원 / 할인가 64,850원 (40% 할인)\",\n", "  \"specifications\": [\n", "    \"상의 핏: 오버/루즈\",\n", "    \"두께감: 적당함\",\n", "    \"신축성: 신축성 좋아요\",\n", "    \"촉감: 적당함\",\n", "    \"안감: 안감이 없어요\",\n", "    \"비침: 비침이 없어요\",\n", "    \"성별 / 계절: 남성 / 여름\",\n", "    \"상품코드: HZTS5B894BK\",\n", "    \"무게: 140g\",\n", "    \"소재: 겉감-나일론 94% 폴리우레탄 6%\",\n", "    \"라벨: https://nimg.lfmall.co.kr/file/product/prd/HZ/2025/375/HZTS5B894BK_L0.jpg\",\n", "    \"색상: 블랙\",\n", "    \"치수: 상단표기\",\n", "    \"제조사: LF\",\n", "    \"제조국: 베트남\",\n", "    \"세탁방법 및 취급시 주의사항: 1.세탁기 세탁 시 손상될 수 있으니 삼가 주십시오. 2.표백성 세제를 삼가 주시고 물에 오랜 시간 (10분 이상) 담가두지 마십시오. 3.제품의 보호를 위해 반드시 세탁망에 넣어서 세탁하십시오.\",\n", "    \"제조연월: 2025년 01월\",\n", "    \"품질보증기준: 구매일로부터 1년간/ 그 외 기준은 관련법 및 소비자분쟁해결 규정에 따름\",\n", "    \"A/S 책임자와 전화번호: LF 고객상담실 1544-5114\"\n", "  ],\n", "  \"features\": [\n", "    \"블랙 컬러는 6월 17일 이후 재입고분부터 더 짙은 톤으로 변경\",\n", "    \"가볍고 시원한 소재\",\n", "    \"데일리 룩에 적합\",\n", "    \"오버/루즈 핏\",\n", "    \"신축성 좋음\",\n", "    \"여름용 남성 티셔츠\"\n", "  ],\n", "  \"keywords\": [\n", "    \"블랙\",\n", "    \"쿨링\",\n", "    \"벤추리\",\n", "    \"헨리넥\",\n", "    \"티셔츠\",\n", "    \"오버핏\",\n", "    \"여름\",\n", "    \"남성\",\n", "    \"나일론\",\n", "    \"폴리우레탄\",\n", "    \"시즌오프\",\n", "    \"LF몰 단독\"\n", "  ]\n", "}\n", "'[25SS][시즌오프][LF몰 단독] 블랙 쿨링 벤추리 헨리넥 티셔츠' 상품 정보를 추가 저장했습니다.\n"]}], "source": ["# LF몰\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from bs4 import BeautifulSoup\n", "import time\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from textwrap import dedent\n", "from uuid import uuid4\n", "\n", "# 1. Selenium 드라이버 실행\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))\n", "url = \"https://www.lfmall.co.kr/app/product/HSTS5B206B2?has_init=Y\"\n", "# url = \"https://www.lfmall.co.kr/app/exhibition/menu/502\"\n", "url = \"https://www.lfmall.co.kr/app/product/HZTS5B894BK\"\n", "url = \"https://www.ssfshop.com/ranking?rankSect=CLICK_RANK&ctgryFlterCd=CTGRY_FEMALE&preferAgeCd=ALL&brndShopId=&brandShopNo=&dspCtgryNo=&otltYn=&cnncCtgryNo=&lagSpcltyYn=&utag=\"\n", "\n", "driver.get(url)\n", "time.sleep(3)  # 페이지 로딩 대기\n", "\n", "# 2. 버튼 클릭 (예: '더보기' 버튼)\n", "try:\n", "    button = driver.find_element(By.CSS_SELECTOR, \"#tabsContainer > div:nth-child(2) > div > div.ProductDetail_detailInner__7p4Ln > div.ProductDetail_buttonBox__SWZ-0 > button\")\n", "    button.click()\n", "    time.sleep(4)  # 클릭 후 로딩 대기\n", "except Exception as e:\n", "    print(\"버튼 클릭 실패:\", e)\n", "\n", "# 3. 클릭 후 현재 페이지 소스 가져오기\n", "html_after_click = driver.page_source\n", "soup = BeautifulSoup(html_after_click, \"html.parser\")\n", "\n", "# 4. 특정 div 영역만 추출\n", "target_1 = soup.select_one(\"#tabsContainer > div:nth-child(2) > div\")# > div.ProductDetail_detailInner__7p4Ln\")\n", "target_2 = soup.select_one(\"#root > main > div > div.Product_divideWrap__\\+Gvda > div.Product_rightWrap__vLWpx > div:nth-child(1)\")\n", "\n", "# 둘 중 없는 경우 대비\n", "target_1_text = str(target_1) if target_1 else \"\"\n", "target_2_text = str(target_2) if target_2 else \"\"\n", "\n", "# 5. 두 영역 합치기\n", "extracted_html = target_1_text + \"\\n\\n\" + target_2_text\n", "\n", "# 6. LL<PERSON> 모델 준비\n", "model = ChatOpenAI(model=\"gpt-4.1\", temperature=0)\n", "\n", "# 7. Prompt Template (중괄호 이스케이프)\n", "prompt_template = ChatPromptTemplate.from_template(dedent(\"\"\"\n", "다음은 상품 상세 페이지의 HTML 일부 소스입니다.\n", "\n", "이 HTML에는 상품에 대한 다양한 정보가 포함되어 있습니다.  \n", "다음 조건에 맞게 JSON 형식으로 변환해주세요:\n", "\n", "1. 상품과 관련된 정보는 가능한 한 모두 담아주세요.  \n", "2. HTML 내 존재하는 정보는 최대한 구체적으로 추출해 주세요.  \n", "3. 아래와 같은 JSON 구조를 따르세요. 단, 'json', ' \\\"\\\"\\\" ', **설명** 등과 같이 불필요한 값은 채워넣지 않습니다.\n", "4. 만약 특정 항목이 HTML에 없다면 해당 항목은 생략하지 말고 빈 문자열(\"\")로 채워 주세요.  \n", "5. specifications과 features 항목은 표, 리스트, 상세 스펙 등이 있을 경우만 포함하고, 없으면 빈 리스트로 남겨 주세요.\n", "\n", "JSON 출력 예시 형태:\n", "\n", "{{\n", "  \"id\": {product_id}\n", "  \"name\": \"상품명\",\n", "  \"category\": \"카테고리 또는 분류명\",\n", "  \"description\": \"상세 설명. 길어도 좋음.\",\n", "  \"price\": \"가격 정보 (할인 전/후 가격 포함 가능)\",\n", "  \"specifications\": [\n", "    \"스펙 1\",\n", "    \"스펙 2\",\n", "    ...\n", "  ],\n", "  \"features\": [\n", "    \"특징 1\",\n", "    \"특징 2\",\n", "    ...\n", "  ],\n", "  \"keywords\": [\n", "    \"키워드 1\",\n", "    \"키워드 2\",\n", "    ...\n", "  ]\n", "}}\n", "\n", "다음은 HTML 소스입니다:\n", "{input_data}\n", "\n", "\"\"\"))\n", "\n", "# 8. 체인 실행\n", "product_id = uuid4()\n", "chain = prompt_template | model\n", "result = chain.invoke({\"input_data\": extracted_html, \"product_id\": product_id})\n", "\n", "# 9. 결과 출력\n", "print(result.content)\n", "\n", "# 10. 드라이버 종료\n", "driver.quit()\n", "\n", "# 11. 파일에 저장\n", "import json\n", "import os\n", "\n", "new_data_text = result.content  # LLM 응답 JSON 문자열\n", "\n", "try:\n", "    new_data = json.loads(new_data_text)\n", "except json.JSONDecodeError as e:\n", "    print(\"JSON 파싱 에러:\", e)\n", "    new_data = None\n", "\n", "if new_data is None:\n", "    print(\"유효한 JSON 데이터가 아니므로 종료합니다.\")\n", "else:\n", "    filename = 'product_info.json'\n", "\n", "    # 기존 데이터 불러오기 (파일 없으면 빈 리스트로 시작)\n", "    if os.path.exists(filename):\n", "        with open(filename, 'r', encoding='utf-8') as f:\n", "            try:\n", "                existing_data = json.load(f)\n", "            except json.JSONDecodeError:\n", "                existing_data = []\n", "    else:\n", "        existing_data = []\n", "\n", "    # existing_data가 리스트인지 확인. 아니면 리스트로 감싸기\n", "    if not isinstance(existing_data, list):\n", "        existing_data = [existing_data]\n", "\n", "    # 같은 name 있는지 검사\n", "    exists = any(item.get('name') == new_data.get('name') for item in existing_data)\n", "\n", "    if exists:\n", "        print(f\"'{new_data.get('name')}' 상품이 이미 존재합니다. 추가하지 않습니다.\")\n", "    else:\n", "        existing_data.append(new_data)\n", "        with open(filename, 'a', encoding='utf-8') as f:\n", "            json.dump(existing_data, f, ensure_ascii=False, indent=2)\n", "        print(f\"'{new_data.get('name')}' 상품 정보를 추가 저장했습니다.\")"]}, {"cell_type": "code", "execution_count": 45, "id": "4d028167", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"name\": \"[25SS][시즌오프]티어드 헤지 데님 반팔 원피스 딥블루\",\n", "  \"category\": \"원피스\",\n", "  \"description\": \"은은한 워싱이 돋보이며 셔링 장식이 어우러져 여성스러움을 살린 반팔 데님 원피스입니다. 면 100% 소재를 사용하여 착용감이 편안하며 별도 탈착 벨트로 다양한 실루엣 연출이 가능한 아이템입니다.\",\n", "  \"price\": \"정가 399,000원 / 할인가 287,280원 (28% 할인)\",\n", "  \"specifications\": [\n", "    \"핏 정보: 상의 핏 - 레귤러, 하의스타일 - A라인, 플레어/플리츠\",\n", "    \"착용 정보: 두께감 - 적당함, 신축성 - 신축성 없어요, 촉감 - 적당함, 안감 - 안감이 없어요, 비침 - 비침이 없어요\",\n", "    \"성별 / 계절: 여성 / 여름\",\n", "    \"상품코드: HSDR5B301B3\",\n", "    \"무게: 565g\",\n", "    \"소재: 겉감,배색-면 100%\",\n", "    \"색상: 다크블루\",\n", "    \"치수: 상단표기\",\n", "    \"제조사: LF\",\n", "    \"제조국: 베트남\",\n", "    \"세탁방법 및 취급시 주의사항: 1.소재 특성상 물 빠짐의 우려가 있으니 반드시 1회 단독 세탁 후 착용하십시오. (드라이클리닝 불가) 2.세탁기 세탁 시 손상될 수 있으니 삼가 주십시오. 3.표백성 세제를 삼가 주시고 물에 오랜시간(10분 이상) 담가 두지 마십시오.\",\n", "    \"제조연월: 2025년 01월\",\n", "    \"품질보증기준: 구매일로부터 1년간/ 그 외 기준은 관련법 및 소비자분쟁해결 규정에 따름\",\n", "    \"A/S 책임자와 전화번호: LF 고객상담실 1544-5114\"\n", "  ],\n", "  \"features\": [\n", "    \"은은한 워싱과 셔링 장식으로 여성스러운 디자인\",\n", "    \"면 100% 소재로 편안한 착용감\",\n", "    \"별도 탈착 벨트로 다양한 실루엣 연출 가능\",\n", "    \"여름용 반팔 데님 원피스\",\n", "    \"신축성 없음, 적당한 두께감, 비침 없음, 안감 없음\"\n", "  ]\n", "}\n"]}], "source": ["print(result.content)"]}], "metadata": {"kernelspec": {"display_name": "lang_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}