"""
벡터 데이터베이스에 문서 임베딩 스크립트
"""
import json
import sys
from pathlib import Path
from typing import List
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

# 환경변수 로드
load_dotenv()

# 프로젝트 루트 경로 설정
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

data_dir = project_root / "data"
vector_db_path = data_dir / "vectordb_chroma"
raw_docs_path = data_dir / "raw_docs"

def load_documents() -> List[Document]:
    """문서 로드"""
    documents = []
    
    # FAQ 문서 로드
    faq_path = raw_docs_path / "faq_data.json"
    if faq_path.exists():
        with open(faq_path, 'r', encoding='utf-8') as f:
            faq_data = json.load(f)
            
        for faq in faq_data:
            doc = Document(
                page_content=f"질문: {faq['question']}\n답변: {faq['answer']}",
                metadata={
                    "source": "faq",
                    "category": faq['category'],
                    "keywords": faq.get('keywords', '')
                }
            )
            documents.append(doc)
        print(f"✅ FAQ 문서 {len(faq_data)}건 로드 완료")
    else:
        print(f"⚠️ 파일을 찾을 수 없음: {faq_path}")
    
    # 제품 정보 문서 로드
    product_path = raw_docs_path / "product_info.json"
    if product_path.exists():
        with open(product_path, 'r', encoding='utf-8') as f:
            product_data = json.load(f)
            
        for product in product_data:
            # 사양 정보를 문자열로 변환
            specs = product.get('specifications', {})
            specs_text = ", ".join([f"{k}: {v}" for k, v in specs.items()]) if isinstance(specs, dict) else str(specs)
            
            # 특징 정보를 문자열로 변환
            features = product.get('features', [])
            features_text = ", ".join(features) if isinstance(features, list) else str(features)
            
            doc = Document(
                page_content=f"상품명: {product['name']}\n설명: {product['description']}\n사양: {specs_text}\n특징: {features_text}\n가격: {product['price']:,}원",
                metadata={
                    "source": "product",
                    "product_id": product.get('product_id') or product.get('id'),
                    "category": product['category'],
                    "price": product['price'],
                    "keywords": product.get('keywords', '')
                }
            )
            documents.append(doc)
        print(f"✅ 제품 정보 문서 {len(product_data)}건 로드 완료")
    else:
        print(f"⚠️ 파일을 찾을 수 없음: {product_path}")
    
    return documents

def create_vector_store():
    """벡터 스토어 생성"""
    print("📄 문서 로드 중...")
    documents = load_documents()
    
    if not documents:
        print("⚠️ 로드할 문서가 없습니다.")
        return None
    
    print(f"📄 총 {len(documents)}개 문서 로드 완료")
    
    # 텍스트 분할기 초기화
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
        length_function=len,
    )
    
    # 문서 분할
    split_docs = text_splitter.split_documents(documents)
    print(f"📄 {len(split_docs)}개 문서 청크 생성")
    
    # 임베딩 모델 초기화
    print("🔧 임베딩 모델 초기화 중...")
    embeddings = OpenAIEmbeddings()
    
    # 벡터 스토어 생성
    print("💾 벡터 스토어 생성 중...")
    vector_db_path.mkdir(parents=True, exist_ok=True)
    
    vectorstore = Chroma.from_documents(
        documents=split_docs,
        embedding=embeddings,
        persist_directory=str(vector_db_path)
    )
    
    # 저장
    vectorstore.persist()
    print(f"✅ 벡터 스토어 생성 완료: {vector_db_path}")
    
    return vectorstore

def test_vector_store(vectorstore):
    """벡터 스토어 테스트"""
    if not vectorstore:
        return
    
    test_queries = [
        "배송비는 얼마인가요?",
        "무선 이어폰 사양이 어떻게 되나요?",
        "반품은 어떻게 하나요?"
    ]
    
    print("\n🔍 테스트 쿼리 실행:")
    for query in test_queries:
        print(f"\n질문: {query}")
        docs = vectorstore.similarity_search(query, k=1)
        if docs:
            print(f"가장 유사한 문서: {docs[0].page_content[:100]}...")
            print(f"메타데이터: {docs[0].metadata}")
        else:
            print("관련 문서를 찾을 수 없습니다.")

def main():
    """메인 실행 함수"""
    print("🚀 벡터 데이터베이스 임베딩 시작...")

    try:
        # 벡터 스토어 생성
        vectorstore = create_vector_store()
        
        # 테스트 쿼리 실행
        test_vector_store(vectorstore)
        
        print("\n✅ 벡터 데이터베이스 임베딩 완료!")
        return 0
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())