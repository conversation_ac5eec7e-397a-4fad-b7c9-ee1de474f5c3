{"cells": [{"cell_type": "code", "execution_count": null, "id": "da7ed252", "metadata": {}, "outputs": [], "source": ["# 역할 분담\n", "\n", "# 파일 로드 - 제품설명서 + FAQ 목록 -> vectorDB 에 넣을 거\n", "## 가져와서 metadata 랑 chunking 전처리하고 vectorDB에 넣기까지\n", "\n", "# RAG 구조 + 모델링 - 메타 데이터 및 쿼리 재생성 + vectorDB에서 검색\n", "## SQL Agent 까지 여기서 구성 \n", "\n", "# 개인정보용 SQLDataBase 구성 - 고객 정보 테이블, 고객 장바구니?, 고객 주문내역\n", "## 해당 정보 관련 query 일 경우 별도의 tool로 개인정보 가져오기\n", "\n", "# RAG 평가 지표 구성\n", "## RAGAS 이용, 지표 골라야함"]}, {"cell_type": "code", "execution_count": null, "id": "49a31ed1", "metadata": {}, "outputs": [], "source": ["# 요구 패키지\n", "%pip install unstructured[md]\n", "%pip install bs4"]}, {"cell_type": "code", "execution_count": 4, "id": "a746c599", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15 15 15\n", "11 11 11\n", "14 14 14\n", "14 14 14\n", "18 18 18\n", "69 69 69\n", "141 \n", " page_content='<p>보유한 적립금이 소멸되기 전 메일로 내용을 안내드리고 있습니다.<br/>\n", "적립금 소멸 안내 상황은 아래에서 확인해 주세요. <br/>\n", "<br/>\n", "■ 적립금 소멸 안내 상황<br/>\n", "1. 보유 적립금 유효기간 만료 예정 <br/>\n", "- 보유 중인 적립금의 유효기간이 만료되기 30일 전, 소멸 예정 안내 메일이 발송됩니다. <br/>\n", "※ 적립금은 유효기간 내 사용 시 정상적으로 이용하실 수 있습니다.<br/>\n", "<br/>\n", "2. 1년 이상 미이용 시 <br/>\n", "- 무신사 스토어 이용약관에 따라, 1년 이상 로그인 이력이 없는 경우 적립금이 소멸됩니다. <br/>\n", "- 11개월간 로그인하지 않은 상태에서 적립금을 보유한 회원에게  적립금 일괄 소멸 30일 전 안내 메일이 발송됩니다. <br/>\n", "※ 적립금 소멸을 원하지 않으실 경우, 소멸 예정일 이전에 로그인해 주세요.</p>' metadata={'source': 'FAQ', 'tag': '<em class=\"faq-list__category\">탈퇴/기타</em>', 'title': '[<p class=\"faq-list__question\">적립금 소멸 예정 메일을 받았어요.</p>, <p class=\"faq-list__question\">회원 탈퇴를 취소하고 싶습니다.</p>, <p class=\"faq-list__question\">회원 탈퇴는 어떻게 하나요?</p>, <p class=\"faq-list__question\">소셜 로그인(카카오, Apple) 연동을 해제하고 싶어요. 어떻게 하면 되나요?</p>, <p class=\"faq-list__question\">애플(Apple) 로그인 및 애플(Apple) 계정 연동이 안 됩니다.</p>, <p class=\"faq-list__question\">소셜 로그인은 어떻게 이용하나요?</p>, <p class=\"faq-list__question\">아이디와 비밀번호가 기억나지 않아요.</p>, <p class=\"faq-list__question\">아이디 및 비밀번호를 변경할 수 있나요?</p>, <p class=\"faq-list__question\">회원 정보 수정은 어디서 하나요?</p>, <p class=\"faq-list__question\">휴대폰 번호 인증 시 이미 존재하는 휴대폰이라고 뜰 때는 어떻게 해야 하나요?</p>, <p class=\"faq-list__question\">본인인증 없이 상품을 구매할 수 있나요?</p>, <p class=\"faq-list__question\">본인인증을 잘못했는데 초기화 가능한가요?</p>, <p class=\"faq-list__question\">본인인증 문자가 오지 않아요.</p>, <p class=\"faq-list__question\">본인인증은 어떻게 하나요?</p>, <p class=\"faq-list__question\">회원 가입은 어떻게 하나요?</p>]'}\n"]}], "source": ["# BeautifulSoup 을 이용하여 FAQ 추출\n", "## 제품설명서도 따와야함 \n", "import requests\n", "from bs4 import BeautifulSoup\n", "from langchain_core.documents import Document\n", "from uuid import uuid4\n", "\n", "documents = []\n", "\n", "for i in range(6):\n", "    # 파싱 과정\n", "    url = f\"https://www.musinsa.com/app/cs/faq/00{i}\"\n", "    response = requests.get(url)\n", "    soup = BeautifulSoup(response.text, \"html.parser\")\n", "    result_tag = soup.select(\"body > main > section > ul > li > button > em\")\n", "    result_title = soup.select(\"body > main > section > ul > li > button > p\")\n", "    result_content = soup.select(\"body > main > section > ul > li > div > p\")\n", "\n", "    print(len(result_tag), len(result_title), len(result_content))\n", "\n", "    # Document 타입으로 저장\n", "    for j in range(len(result_tag)):\n", "        doc = Document(\n", "            page_content=str(result_content[j]),\n", "            metadata={\"source\":\"FAQ\", \n", "                    \"tag\":str(result_tag[j]), \n", "                    \"title\":str(result_title)},\n", "            id=str(uuid4())\n", "            )\n", "        documents.append(doc)\n", "print(len(documents), '\\n', documents[0])"]}, {"cell_type": "code", "execution_count": null, "id": "b97271c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'source': 'FAQ',\n", " 'tag': '<em class=\"faq-list__category\">탈퇴/기타</em>',\n", " 'title': '[<p class=\"faq-list__question\">적립금 소멸 예정 메일을 받았어요.</p>, <p class=\"faq-list__question\">회원 탈퇴를 취소하고 싶습니다.</p>, <p class=\"faq-list__question\">회원 탈퇴는 어떻게 하나요?</p>, <p class=\"faq-list__question\">소셜 로그인(카카오, Apple) 연동을 해제하고 싶어요. 어떻게 하면 되나요?</p>, <p class=\"faq-list__question\">애플(Apple) 로그인 및 애플(Apple) 계정 연동이 안 됩니다.</p>, <p class=\"faq-list__question\">소셜 로그인은 어떻게 이용하나요?</p>, <p class=\"faq-list__question\">아이디와 비밀번호가 기억나지 않아요.</p>, <p class=\"faq-list__question\">아이디 및 비밀번호를 변경할 수 있나요?</p>, <p class=\"faq-list__question\">회원 정보 수정은 어디서 하나요?</p>, <p class=\"faq-list__question\">휴대폰 번호 인증 시 이미 존재하는 휴대폰이라고 뜰 때는 어떻게 해야 하나요?</p>, <p class=\"faq-list__question\">본인인증 없이 상품을 구매할 수 있나요?</p>, <p class=\"faq-list__question\">본인인증을 잘못했는데 초기화 가능한가요?</p>, <p class=\"faq-list__question\">본인인증 문자가 오지 않아요.</p>, <p class=\"faq-list__question\">본인인증은 어떻게 하나요?</p>, <p class=\"faq-list__question\">회원 가입은 어떻게 하나요?</p>]'}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["documents[0].metadata"]}, {"cell_type": "code", "execution_count": 5, "id": "ceaafb5f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3,\n", " {'page_content': '<p><span style=\"font-size:14px;\">■ 적립금이란?<br/>\\n상품을 구매하거나 구매한 상품의 후기 작성, 이벤트 참여 등을 통해 지급됩니다.<br/>\\n* 지급된 적립금은 상품 구매 시, 결제 금액의 최대 7%까지 사용할 수 있습니다.<br/>\\n* 적립금은 10원 단위로만 사용 가능합니다.<br/>\\n* 주문 시 적립금 대신 적립금 선할인을 통해 할인을 받을 수도 있습니다. (단, 현금으로 환급은 불가)<br/>\\n* 제휴(광고) 접속 여부 또는 일부 상품은 구매 적립 및 적립금 선할인이 제한될 수 있습니다.<br/>\\n<br/>\\n■ 적립금 확인하기<br/>\\n- 모바일(앱/웹) : 마이 &gt; 적립금에서 확인<br/>\\n<br/>\\n※ 적립금 전환 : 포인트 제도 변경으로 2024년 7월 28일부터 포인트를 적립금으로 전환할 수 없습니다.<br/>\\n<br/>\\n<strong><a href=\"https://www.musinsa.com/mypage/legacy/point\"><span style=\"color:#3498db;\">포인트 적립 확인 바로가기</span></a><br/>\\n<a href=\"https://www.musinsa.com/app/cs/notice_list#no_14497\"><span style=\"color:#3498db;\">포인트 제도 일부 변경 안내 바로가기</span></a></strong></span></p>',\n", "  'metadata': {'source': 'FAQ',\n", "   'tag': '<em class=\"faq-list__category\">혜택</em>',\n", "   'title': '[<p class=\"faq-list__question\">무신사 스탠다드 익스프레스는 뭔가요?</p>, <p class=\"faq-list__question\">오프라인 구매 상품 후기는 어떻게 작성하나요?</p>, <p class=\"faq-list__question\">후기 규정을 알려주세요.</p>, <p class=\"faq-list__question\">발매판이 궁금해요.</p>, <p class=\"faq-list__question\">추천판이 궁금해요.</p>, <p class=\"faq-list__question\">한달 후기에 대해 알려주세요.</p>, <p class=\"faq-list__question\">포인트를 확인하고 싶어요.</p>, <p class=\"faq-list__question\">상품 검색 시 정렬 기준은 어떻게 되나요?</p>, <p class=\"faq-list__question\">나이키 브랜드 상품은 A/S가 가능한가요?</p>, <p class=\"faq-list__question\">부티크 상품 A/S 진행 상황은 어떻게 확인할 수 있나요?</p>, <p class=\"faq-list__question\">무배당발 환불 서비스는 무엇인가요?</p>, <p class=\"faq-list__question\">무배당발 교환 서비스는 무엇인가요?</p>, <p class=\"faq-list__question\">무배당발 서비스는 무엇인가요?</p>, <p class=\"faq-list__question\">부티크 스트랩 커스텀 서비스 A/S는 가능한가요?</p>, <p class=\"faq-list__question\">부티크 스트랩 커스텀 서비스는 교환/반품이 가능한가요?</p>, <p class=\"faq-list__question\">부티크 스트랩 커스텀 서비스는 어떻게 이용하나요?</p>, <p class=\"faq-list__question\">부티크 라이프 스타일 상품은 A/S를 받을 수 있나요?</p>, <p class=\"faq-list__question\">부티크 라이프 스타일 상품은 교환/반품이 가능한가요?</p>, <p class=\"faq-list__question\">부티크 라이프 스타일 배송은 어떻게 이뤄지나요?</p>, <p class=\"faq-list__question\">부티크 라이프 스타일 상품을 받았는데 불량같아요.</p>, <p class=\"faq-list__question\">부티크 컬렉터블 상품은 교환/반품이 가능한가요?</p>, <p class=\"faq-list__question\">부티크 컬렉터블 상품의 패키지와 구성품은 어떻게 제공되나요?</p>, <p class=\"faq-list__question\">부티크 컬렉터블 상품의 배송은 어떻게 이뤄지나요?</p>, <p class=\"faq-list__question\">부티크 컬렉터블 상품 구매 시 유의 사항이 있나요?</p>, <p class=\"faq-list__question\">무신사 부티크 상품은 전부 정품인가요?</p>, <p class=\"faq-list__question\">부티크 타임피스는 무엇인가요?</p>, <p class=\"faq-list__question\">부티크 타임피스 상품은 A/S를 받을 수 있나요?</p>, <p class=\"faq-list__question\">테라스 이용 혜택 및 유의사항에 대해 알려주세요.</p>, <p class=\"faq-list__question\">무신사 테라스 이용은 어떻게 하나요?</p>, <p class=\"faq-list__question\">적립금에 유효기간이 있나요?</p>, <p class=\"faq-list__question\">스냅은 무엇이고 어떻게 이용하나요?</p>, <p class=\"faq-list__question\">랭킹은 무엇이고 어떻게 정해지나요?</p>, <p class=\"faq-list__question\">APP 이용에 오류가 있어요 어떻게 해결하나요?</p>, <p class=\"faq-list__question\">사이즈를 간편하게 확인할 수 있는 기능이 있나요?</p>, <p class=\"faq-list__question\">관심 있는 브랜드의 소식을 받아볼 수 있나요?</p>, <p class=\"faq-list__question\">알림 설정(앱 푸시)은 어떻게 하는 건가요?</p>, <p class=\"faq-list__question\">부티크 보안실이 무엇인가요?</p>, <p class=\"faq-list__question\">아울렛은 무엇인가요?</p>, <p class=\"faq-list__question\">키즈란 무엇인가요?</p>, <p class=\"faq-list__question\">부티크 배송받은 상품이 상품 상세 사진과 다른 거 같아요.</p>, <p class=\"faq-list__question\">부티크 상품 구성(박스, 쇼핑백, 선물 포장, 보증서 등)은 어떻게 되나요?</p>, <p class=\"faq-list__question\">부티크 상품 배송은 얼마나 걸리나요?</p>, <p class=\"faq-list__question\">부티크란 무엇인가요?</p>, <p class=\"faq-list__question\">오프라인 스토어에서 상품을 픽업할 수 있나요?</p>, <p class=\"faq-list__question\">오프라인 스토어 정보는 어디서 확인할 수 있나요?</p>, <p class=\"faq-list__question\">무신사 스탠다드란 무엇인가요?</p>, <p class=\"faq-list__question\">무신사 라이브에 대해 알려주세요.</p>, <p class=\"faq-list__question\">래플에 당첨되었어요, 구매는 어떻게 하나요?</p>, <p class=\"faq-list__question\">래플 이용 방법에 대해 알려주세요.</p>, <p class=\"faq-list__question\">신규 회원 특별 혜택 이벤트는 무엇인가요?</p>, <p class=\"faq-list__question\">친구 초대 이벤트란 무엇인가요?</p>, <p class=\"faq-list__question\">후기 댓글에 욕설이 있어요. 어떻게 해야 하나요?</p>, <p class=\"faq-list__question\">후기 삭제는 어떻게 하나요?</p>, <p class=\"faq-list__question\">후기의 종류를 알려주세요.</p>, <p class=\"faq-list__question\">상품이 불량인 것을 착용하고나서 확인했어요 어떻게 하나요?</p>, <p class=\"faq-list__question\">구매한 상품을 사용하던 중 A/S가 필요한 경우 어떻게 해야 하나요?</p>, <p class=\"faq-list__question\">무신사 에듀란 무엇인가요?</p>, <p class=\"faq-list__question\">무신사 고객센터는 어떻게 이용하나요?</p>, <p class=\"faq-list__question\">오프라인 쿠폰이 등록되지 않아요.</p>, <p class=\"faq-list__question\">결제하는 방법에 따라 할인 이벤트가 있나요?</p>, <p class=\"faq-list__question\">구매 시 사용할 수 있는 할인 혜택은 어떤 게 있나요?</p>, <p class=\"faq-list__question\">무신사에 입점하려면 어떻게 하나요?</p>, <p class=\"faq-list__question\">회원 등급에 따라 어떤 혜택을 받을 수 있나요?</p>, <p class=\"faq-list__question\">생일 쿠폰은 언제 발급되나요?</p>, <p class=\"faq-list__question\">쿠폰은 어떻게 사용하나요?</p>, <p class=\"faq-list__question\">적립금은 언제나 사용할 수 있나요?</p>, <p class=\"faq-list__question\">적립금 선 할인은 무엇인가요?</p>, <p class=\"faq-list__question\">환불 시 사용한 적립금은 어떻게 반환되나요?</p>, <p class=\"faq-list__question\">적립금은 무엇인가요?</p>]'},\n", "  'id': 'efa37e28-93c7-45a2-96e1-f22784884492'})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Document 들 저장 - <PERSON><PERSON>\n", "## 내부 내용 다듬기는 해야함\n", "import json\n", "import os\n", "\n", "os.makedirs(\"data\", exist_ok=True)\n", "\n", "documents_dict = []\n", "for doc in documents:\n", "    doc_dict = {\"page_content\": doc.page_content, \"metadata\": doc.metadata, \"id\": doc.id}\n", "    documents_dict.append(doc_dict)\n", "dir_name = \"../data/\"\n", "file_name = \"documents.json\"\n", "with open(os.path.join(dir_name, file_name), \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(documents_dict, f, ensure_ascii=False, indent=2)\n", "len(doc_dict), doc_dict"]}, {"cell_type": "code", "execution_count": null, "id": "1c9f0bb8", "metadata": {}, "outputs": [], "source": ["# Chunking 과정 "]}, {"cell_type": "code", "execution_count": 6, "id": "5b1035ea", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.\n", "INFO: HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"data": {"text/plain": ["141"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 임베딩 + Vector Store 구성\n", "## FAQ_db 랑 instructions_db 두 개 구성하기 \n", "from langchain_openai import OpenAIEmbeddings\n", "from dotenv import load_dotenv\n", "from langchain_chroma import Chroma\n", "\n", "COLLECTION_NAME = \"product_usage\"\n", "PERSISTENT_PATH = \"../data/vector_store/chroma/FAQ_db\"\n", "\n", "# Vector Store 생성\n", "def get_vector_store():\n", "    load_dotenv()\n", "    embedding_model = OpenAIEmbeddings(model='text-embedding-3-large')\n", "\n", "    vector_store = Chroma(\n", "        embedding_function=embedding_model,\n", "        collection_name=COLLECTION_NAME,\n", "        persist_directory=PERSISTENT_PATH\n", "    )\n", "    return vector_store\n", "vector_store = get_vector_store()\n", "\n", "# 문서 추가 \n", "vector_store.add_documents(documents)\n", "vector_store._collection.count()"]}, {"cell_type": "code", "execution_count": null, "id": "17455974", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7215f3a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4d54b70", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lang_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}